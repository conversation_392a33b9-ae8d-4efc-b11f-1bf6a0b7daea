import asyncio
import logging
import time
import hashlib
from typing import Dict, List, Optional, Any
from qdrant_client import AsyncQdrantClient, models
from fastembed import TextEmbedding, SparseTextEmbedding, LateInteractionTextEmbedding
import json
from datetime import datetime, timedelta

# Import ADK components
from google.adk.tools import ToolContext, LongRunningFunctionTool

from multi_tool_agent.config import AgentConfig
from .schemas import ActivityFilters

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Simple in-memory cache for frequently accessed queries
_search_cache = {}
_cache_ttl = AgentConfig.CACHE_TTL_SECONDS  # Configurable TTL

# Initialize all models once at the module level for performance
dense_embedder = TextEmbedding(model_name=AgentConfig.EMBEDDING_MODEL)
sparse_embedder = SparseTextEmbedding(model_name=AgentConfig.SPARSE_EMBEDDING_MODEL)
colbert_embedder = LateInteractionTextEmbedding(model_name=AgentConfig.COLBERT_MODEL)

def _create_qdrant_filter(filters: Dict[str, Any]) -> Optional[models.Filter]:
    """Creates a Qdrant filter from a structured filter object."""
    must_conditions = []
    
    # Location filtering using city field
    if location := filters.get("location"):
        if "new west" in location.lower() or "new westminster" in location.lower():
            must_conditions.append(models.FieldCondition(key="city", match=models.MatchValue(value="New Westminster")))
        elif "burnaby" in location.lower():
            must_conditions.append(models.FieldCondition(key="city", match=models.MatchValue(value="Burnaby")))
    
    # Price Filtering
    if "max_price" in filters:
        try:
            max_price = float(filters["max_price"])
            must_conditions.append(models.FieldCondition(key="price_numeric", range=models.Range(lte=max_price)))
        except (ValueError, TypeError):
            logger.warning(f"Invalid max_price format: {filters['max_price']}")
    
    # Date Filtering
    if "date" in filters:
        try:
            # Assuming date is in 'YYYY-MM-DD' format
            target_date = filters["date"]
            # The activity must start on or before the target date AND end on or after it.
            must_conditions.append(models.FieldCondition(key="start_date", range=models.Range(lte=target_date)))
            must_conditions.append(models.FieldCondition(key="end_date", range=models.Range(gte=target_date)))
        except (ValueError, TypeError):
            logger.warning(f"Invalid date format: {filters['date']}")

    # Open Status Filtering
    if filters.get("is_open") is True:
        must_conditions.append(models.FieldCondition(key="is_open", match=models.MatchValue(value=True)))
    
    # Name Contains Filtering - Now enabled with text index on name field
    if name_filter := filters.get("name_contains"):
        must_conditions.append(models.FieldCondition(key="name", match=models.MatchText(text=name_filter)))

    # Age and Day of Week filtering
    if age := filters.get("age"):
        must_conditions.append(models.FieldCondition(key="min_age_years", range=models.Range(lte=age)))
        must_conditions.append(models.FieldCondition(key="max_age_years", range=models.Range(gte=age)))

    # Robustly handle the day_of_week filter
    if day_filters := filters.get("day_of_week"):
        # If the LLM provides a string, convert it to a list of one
        if isinstance(day_filters, str):
            day_filters_list = [day_filters]
        # If it's already a list, use it directly
        elif isinstance(day_filters, list):
            day_filters_list = day_filters
        # Otherwise, log a warning and skip this filter
        else:
            logger.warning(f"Unsupported type for 'day_of_week' filter: {type(day_filters)}. Skipping.")
            day_filters_list = None

        if day_filters_list:
            # Ensure all items in the list are strings, just in case
            safe_day_filters = [str(day).capitalize() for day in day_filters_list]
            must_conditions.append(
                models.FieldCondition(key="days_of_week_list", match=models.MatchAny(any=safe_day_filters))
            )

    return models.Filter(must=must_conditions) if must_conditions else None

async def _perform_advanced_search(query: str, qdrant_filter: Optional[models.Filter], limit: int = 10) -> List[Dict]:
    """
    Performs a state-of-the-art 3-stage reranking funnel:
    Stage 1: Ultra-fast hybrid search (dense + sparse) with binary quantization
    Stage 2: Fine-grained ColBERT reranking for precision
    Stage 3: Final scoring and ranking
    """
    # Performance monitoring
    start_time = time.time()

    # Simple cache key generation
    cache_key = hashlib.md5(f"{query}_{str(qdrant_filter)}_{limit}".encode()).hexdigest()

    # Check cache first
    if cache_key in _search_cache:
        cached_result, cache_time = _search_cache[cache_key]
        if time.time() - cache_time < _cache_ttl:
            logger.info(f"⚡ Cache hit for query: {query[:50]}... (took {time.time() - start_time:.3f}s)")
            return cached_result

    qdrant_client = AsyncQdrantClient(url=AgentConfig.QDRANT_URL, api_key=AgentConfig.QDRANT_API_KEY)
    try:
        # Generate all necessary query vectors from the single, smart query
        dense_vector = list(dense_embedder.query_embed(query))[0].tolist()
        sparse_vector_obj = list(sparse_embedder.query_embed(query))[0]
        colbert_multivector = list(colbert_embedder.query_embed(query))[0].tolist()

        # STAGE 1: Ultra-fast hybrid retrieval with binary quantization
        # Adaptive candidate selection based on mode
        if AgentConfig.ULTRA_FAST_MODE:
            stage1_limit = max(limit * 3, AgentConfig.FAST_MODE_CANDIDATE_LIMIT)  # Aggressive speed optimization
            logger.info(f"⚡ ULTRA-FAST Mode: Retrieving {stage1_limit} candidates")
        else:
            stage1_limit = max(limit * AgentConfig.STAGE1_CANDIDATE_MULTIPLIER, AgentConfig.MIN_STAGE1_CANDIDATES)
            logger.info(f"🚀 Stage 1: Hybrid search retrieving {stage1_limit} candidates")

        # Use the optimized 3-stage query with binary quantization for speed
        query_response = await qdrant_client.query_points(
            collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
            prefetch=[
                # Dense vector search with binary quantization (ultra-fast)
                models.Prefetch(
                    query=dense_vector,
                    using="dense",
                    limit=stage1_limit
                ),
                # Sparse vector search for keyword matching
                models.Prefetch(
                    query=models.SparseVector(
                        indices=sparse_vector_obj.indices.tolist(),
                        values=sparse_vector_obj.values.tolist()
                    ),
                    using="sparse",
                    limit=stage1_limit
                ),
            ],
            # STAGE 2: ColBERT reranking for fine-grained precision
            query=colbert_multivector,
            using="colbert",
            query_filter=qdrant_filter,
            limit=limit,
            with_payload=True,
            # Enable score threshold for quality control
            score_threshold=AgentConfig.RERANK_SCORE_THRESHOLD
        )

        results = [point.payload for point in query_response.points]

        # Performance logging and caching
        total_time = time.time() - start_time
        logger.info(f"✅ 3-stage search completed: {len(results)} high-precision results in {total_time:.3f}s")

        # Cache the results for future queries
        _search_cache[cache_key] = (results, time.time())

        # Clean old cache entries (simple LRU-like behavior)
        if len(_search_cache) > 100:  # Keep cache size reasonable
            oldest_key = min(_search_cache.keys(), key=lambda k: _search_cache[k][1])
            del _search_cache[oldest_key]

        return results

    finally:
        await qdrant_client.close()

# --- Decomposed "Actuator" Tool ---
def _find_back_to_back_activities(activities: List[Dict]) -> List[Dict]:
    """
    Finds consecutive activities that can be taken back-to-back.
    Returns activities grouped by back-to-back opportunities.
    """
    from datetime import datetime, timedelta

    # Group activities by facility and date
    facility_groups = {}
    for activity in activities:
        facility = activity.get("facility", "Unknown")
        start_date = activity.get("start_date")
        if not start_date:
            continue

        key = f"{facility}_{start_date}"
        if key not in facility_groups:
            facility_groups[key] = []
        facility_groups[key].append(activity)

    back_to_back_results = []

    for group_key, group_activities in facility_groups.items():
        # Sort activities by start time
        timed_activities = []
        for activity in group_activities:
            start_time_str = activity.get("start_time_iso")
            if start_time_str:
                try:
                    # Parse time (format: "HH:MM:SS")
                    start_time = datetime.strptime(start_time_str, "%H:%M:%S").time()
                    timed_activities.append((start_time, activity))
                except ValueError:
                    continue

        # Sort by start time
        timed_activities.sort(key=lambda x: x[0])

        # Find consecutive pairs (within 30 minutes)
        for i in range(len(timed_activities) - 1):
            current_time, current_activity = timed_activities[i]
            next_time, next_activity = timed_activities[i + 1]

            # Calculate time difference
            current_end_str = current_activity.get("end_time_iso")
            if current_end_str:
                try:
                    current_end = datetime.strptime(current_end_str, "%H:%M:%S").time()
                    next_start = next_time

                    # Convert to datetime for calculation
                    base_date = datetime.today()
                    current_end_dt = datetime.combine(base_date, current_end)
                    next_start_dt = datetime.combine(base_date, next_start)

                    # Check if next class starts within 30 minutes of current ending
                    time_gap = (next_start_dt - current_end_dt).total_seconds() / 60

                    if 0 <= time_gap <= 30:  # 0-30 minute gap
                        # Create back-to-back pair
                        back_to_back_pair = {
                            "type": "back_to_back_pair",
                            "facility": current_activity.get("facility"),
                            "date": current_activity.get("start_date"),
                            "first_class": current_activity,
                            "second_class": next_activity,
                            "time_gap_minutes": int(time_gap),
                            "total_duration": f"{current_activity.get('start_time_iso', '')} - {next_activity.get('end_time_iso', '')}"
                        }
                        back_to_back_results.append(back_to_back_pair)

                except ValueError:
                    continue

    return back_to_back_results

async def raw_activity_search(
    query: str,
    filters: Dict[str, Any],
    tool_context: ToolContext,
    find_back_to_back: bool = False,
) -> Dict[str, Any]:
    """
    Performs an advanced search for kids' activities and returns the raw,
    unstructured JSON data for another agent to process.
    """
    # This explicit validation is robust and ensures we're working with a typed object.
    try:
        validated_filters = ActivityFilters.model_validate(filters or {})
        filters_dict = validated_filters.model_dump(exclude_none=True)
    except Exception as e:
        logger.warning(f"Could not validate filters: {e}. Proceeding without them.")
        filters_dict = {}

    logger.info(f"🧠 Running RAW search. Query: '{query}', Filters: {filters_dict}, Back-to-back: {find_back_to_back}")
    try:
        final_limit = 40 if find_back_to_back else 20
        qdrant_filter = _create_qdrant_filter(filters_dict)
        activities = await _perform_advanced_search(query, qdrant_filter, limit=final_limit)

        if not activities:
            return {"status": "success", "results": []}

        # If back-to-back search is requested, find consecutive activities
        if find_back_to_back:
            back_to_back_pairs = _find_back_to_back_activities(activities)
            logger.info(f"🔗 Found {len(back_to_back_pairs)} back-to-back opportunities")

            # Return both individual activities and back-to-back pairs
            return {
                "status": "success",
                "results": activities,
                "back_to_back_pairs": back_to_back_pairs,
                "search_type": "back_to_back"
            }

        # Return the raw list of activity dictionaries for the Synthesizer agent
        return {"status": "success", "results": activities}

    except Exception as e:
        logger.error(f"Error in raw_activity_search: {e}", exc_info=True)
        return {"status": "error", "message": "An error occurred during the search."}

# --- Human-in-the-Loop Confirmation Tool ---
def get_user_confirmation(plan: str, tool_context: ToolContext) -> str:
    """
    Presents an execution plan to the user and asks for their confirmation to proceed.
    This is a long-running tool that will pause until the user responds 'yes' or 'no'.
    """
    # This tells the ADK framework not to call an LLM to summarize this tool's output.
    # The 'plan' is meant to be displayed directly in the UI.
    tool_context.actions.skip_summarization = True
    # The return value is None because the framework will pause and wait for the
    # user to send back the confirmation in a separate turn.
    return None

# The ADK framework needs the function to be wrapped in a FunctionTool or LongRunningFunctionTool class.
user_confirmation_tool = LongRunningFunctionTool(func=get_user_confirmation)
